# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# IDE and editor files
.kiro/
.vscode/
.idea/

# Package managers
package-lock.json
npm
dashboard@*

# Backend and build artifacts  
nest-cli.json
tsconfig.build.json

# Generated or temporary files
*.png
next
featurecommands.txt
convert_banner_green.py

# Build output
/dist/
dist/

# Development and testing files (backend branch)
.augment/
.claude/
.github/
.pixi/
__tests__/
e2e/
jest.config.js
jest.setup.js
playwright.config.ts
pnpm-workspace.yaml
scripts/cache-cleanup.js
scripts/cache-diagnostic.js
scripts/cache-validation.js
test-discord-frontend-integration.js
typescript-compilation-fixed.log

# Frontend source code (belongs on master branch, not backend)
src/

# Documentation files (keep on master branch)
DISCORD_BACKEND_INTEGRATION_PLAN.md
README-TESTING.md
cookies.txt
build-output.log
scripts/dev-reset.sh
